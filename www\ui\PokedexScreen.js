// ui/PokedexScreen.js
// Pokedex screen component

import { Component } from './Component.js';
import { gameState } from '../state/game-state.js';
import { logger } from '../utils/logger.js';
import { registerBackButtonHandler } from '../capacitor/app.js';
import { getGermanPokemonName } from '../utils/pokemon-display-names.js';

export class PokedexScreen extends Component {
  constructor(container, options = {}) {
    super(container, options);
    this.pokedex = options.pokedex || gameState.pokedex;
    this.typeColors = this.getTypeColors();
  }

  /**
   * Get type colors from CSS variables
   * @returns {Object} - Map of type to color
   */
  getTypeColors() {
    // List of types as in type-colors.css
    const types = [
      'bug', 'dark', 'dragon', 'electric', 'fairy', 'fighting', 'fire', 'flying',
      'ghost', 'grass', 'ground', 'ice', 'normal', 'poison', 'psychic', 'rock',
      'steel', 'water'
    ];

    const style = getComputedStyle(document.documentElement);
    const typeColors = {};

    types.forEach(type => {
      typeColors[type] = style.getPropertyValue(`--type-${type}`).trim();
    });

    return typeColors;
  }

  /**
   * Render the Pokedex screen
   * @returns {HTMLElement} - The rendered container
   */
  async render() {
    try {
      // Get pokedex data
      let pokedexData = gameState.pokedexData;
      if (!pokedexData || !Array.isArray(pokedexData) || pokedexData.length < 151) {
        try {
          const resp = await fetch('./pokedex-151.json');
          pokedexData = await resp.json();
        } catch (e) {
          logger.error('Error fetching pokedex data:', e);
          pokedexData = [];
        }
      }

      // Create maps for quick access
      const nameToDex = {};
      const idToDex = {};

      (pokedexData || []).forEach(p => {
        nameToDex[(p.name || '').toLowerCase()] = p.dex_number;
        idToDex[p.id] = p.dex_number;
      });

      // Get unique Pokemon by dex number
      const uniqueByDex = {};

      logger.debug(`POKEDEX: Processing ${this.pokedex.length} Pokemon from gameState.pokedex`);

      for (const p of this.pokedex) {
        let dex = p.dex_number;
        if (!dex) {
          // Try to get dex_number from name or id
          dex = nameToDex[(p.name || '').toLowerCase()] || idToDex[p.id];
        }
        if (dex && !uniqueByDex[dex]) {
          uniqueByDex[dex] = { ...p, dex_number: dex };
        }

        // Log specific Pokemon for debugging
        if (p.name === 'arbok' || p.name === 'magmar') {
          logger.debug(`POKEDEX: Processing ${p.name}:`, {
            name: p.name,
            dex_number: p.dex_number,
            calculated_dex: dex,
            image_url: p.image_url,
            imageUrl: p.imageUrl,
            image: p.image,
            willBeIncluded: dex && !uniqueByDex[dex]
          });
        }
      }

      // Sort by dex number
      const sorted = Object.values(uniqueByDex).sort((a, b) => (a.dex_number || 999) - (b.dex_number || 999));
      const total = 151;

      // Log Pokemon count and check for Arbok/Magmar
      logger.debug(`POKEDEX: Rendering ${sorted.length} Pokemon out of ${total}`);
      const arbok = sorted.find(p => p.name === 'arbok');
      const magmar = sorted.find(p => p.name === 'magmar');
      if (arbok) {
        logger.debug(`POKEDEX: Found Arbok in sorted list:`, arbok);
      } else {
        logger.debug(`POKEDEX: Arbok NOT found in sorted list`);
      }
      if (magmar) {
        logger.debug(`POKEDEX: Found Magmar in sorted list:`, magmar);
      } else {
        logger.debug(`POKEDEX: Magmar NOT found in sorted list`);
      }

      // Render the header and grid
      this.container.innerHTML = `
        <div class="screen-header pokedex-header">
          <button class="back-btn" id="pokedex-back-btn" aria-label="Zurück">
            <img src="./icons/materialicons/chevronleft.svg" alt="Zurück" class="icon-svg" width="38" height="38" />
          </button>
          <h1>Pokédex</h1>
          <span class="pokedex-count header-right">${sorted.length} / ${total}</span>
        </div>
        <div class="pokedex-grid">
          ${sorted.map(pokemon => this.renderPokemonCard(pokemon)).join('')}
        </div>
      `;

      // Store elements for event handling
      this.elements.backButton = this.container.querySelector('#pokedex-back-btn');

      this.isRendered = true;
      return this.container;
    } catch (e) {
      logger.error('Error rendering Pokedex screen:', e);
      this.container.innerHTML = `
        <div class="screen-header pokedex-header">
          <button class="back-btn" id="pokedex-back-btn" aria-label="Zurück">
            <img src="./icons/materialicons/chevronleft.svg" alt="Zurück" class="icon-svg" width="38" height="38" />
          </button>
          <h1>Pokédex</h1>
          <span class="pokedex-count header-right">Error</span>
        </div>
        <div class="pokedex-error">
          <p>Error loading Pokedex data. Please try again.</p>
        </div>
      `;

      this.elements.backButton = this.container.querySelector('#pokedex-back-btn');

      this.isRendered = true;
      return this.container;
    }
  }

  /**
   * Render a Pokemon card
   * @param {Object} pokemon - The Pokemon data
   * @returns {string} - The HTML for the card
   */
  renderPokemonCard(pokemon) {
    const [type1, type2] = pokemon.types || [];
    let cardClass = type2 ? '' : `type-bg-${type1}`;
    let cardStyle = '';

    if (type2) {
      cardStyle = `background: linear-gradient(135deg, var(--type-${type1}) 60%, var(--type-${type2}) 80%);`;
    }

    // Get German name for display
    const displayName = getGermanPokemonName(pokemon);

    // Get image URL with fallback
    const imageUrl = pokemon.image_url || pokemon.imageUrl || (pokemon.dex_number ? `./src/PokemonSprites/${pokemon.dex_number}.png` : '');

    // Log detailed information for debugging, especially for Arbok and Magmar
    if (pokemon.name === 'arbok' || pokemon.name === 'magmar' || !imageUrl) {
      logger.debug(`POKEDEX: Rendering card for ${pokemon.name} (${displayName}):`, {
        name: pokemon.name,
        dex_number: pokemon.dex_number,
        image_url: pokemon.image_url,
        imageUrl: pokemon.imageUrl,
        finalImageUrl: imageUrl,
        types: pokemon.types
      });
    }

    return `
      <div class="pokedex-card ${cardClass}"${cardStyle ? ` style="${cardStyle}"` : ''}>
        <div class="pokedex-card-dex">${String(pokemon.dex_number).padStart(3, '0')}</div>
        <div class="pokedex-card-name">${displayName}</div>
        <div class="pokedex-card-types">
          ${(pokemon.types || []).map(t => {
            const typeKey = t.toLowerCase();
            return `<span class="type-label type-${typeKey}">${t}</span>`;
          }).join('')}
        </div>
        <img class="pokedex-card-img" src="${imageUrl}" alt="${displayName}" />
      </div>
    `;
  }

  /**
   * Add event listeners
   */
  addEventListeners() {
    if (this.elements.backButton) {
      this.addEventListener(this.elements.backButton, 'click', () => {
        // Close overlay
        const overlay = this.container.closest('#pokedex-main-overlay');
        if (overlay) {
          overlay.style.display = 'none';
          overlay.dispatchEvent(new Event('closePokedex'));
        }
      });
    }
  }
}

/**
 * Open the Pokedex screen
 */
export function openPokedexScreen() {
  // Import fabManager dynamically to avoid circular dependencies
  import('../ui/FabManager.js').then(({ fabManager }) => {
    // Hide all FAB buttons to prevent errors
    fabManager.hideAllButtons();
  }).catch(e => {
    logger.warn('Could not import fabManager:', e);
  });

  let overlay = document.getElementById('pokedex-main-overlay');

  if (!overlay) {
    overlay = document.createElement('div');
    overlay.id = 'pokedex-main-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100vw';
    overlay.style.height = '100vh';
    overlay.style.zIndex = '10010';
    overlay.style.background = 'var(--standard-background-color)';
    overlay.style.overflowY = 'auto';
    document.body.appendChild(overlay);
  }

  overlay.style.display = 'block';

  const pokedexScreen = new PokedexScreen(overlay, { pokedex: gameState.pokedex });
  pokedexScreen.render();
  pokedexScreen.addEventListeners();

  // Funktion zum Schließen des Overlays
  const closeOverlay = () => {
    overlay.style.display = 'none';
    overlay.dispatchEvent(new Event('closePokedex'));

    // Show all FAB buttons when overlay is closed
    import('../ui/FabManager.js').then(({ fabManager }) => {
      fabManager.showAllButtons();
    }).catch(e => {
      logger.warn('Could not import fabManager:', e);
    });
  };

  // Zurück-Button des Smartphones abfangen
  const removeBackButtonHandler = registerBackButtonHandler(closeOverlay);

  // Close overlay on back event
  overlay.addEventListener('closePokedex', () => {
    // Event-Listener für den Zurück-Button entfernen
    removeBackButtonHandler();
    overlay.style.display = 'none';

    // Show all FAB buttons when overlay is closed
    import('../ui/FabManager.js').then(({ fabManager }) => {
      fabManager.showAllButtons();
    }).catch(e => {
      logger.warn('Could not import fabManager:', e);
    });
  });
}
